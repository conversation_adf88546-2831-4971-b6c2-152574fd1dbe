'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Location, MapComponentProps, MapConfig, LocationType } from '@/types/map';
import { createMarkerIcon } from '@/utils/mapUtils';

const DEFAULT_MAP_CONFIG: MapConfig = {
  center: { lat: 24.7136, lng: 46.6753 }, // Riyadh
  zoom: 6
};

export default function MapComponent({
  locations,
  config = {},
  onLocationClick,
  onMapReady,
  className = ''
}: MapComponentProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);

  const mapConfig = { ...DEFAULT_MAP_CONFIG, ...config };

  // Load Google Maps API
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Check if already loaded
    if (window.google && window.google.maps) {
      setIsLoaded(true);
      return;
    }

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}`;
    script.async = true;
    script.defer = true;

    script.onload = () => setIsLoaded(true);
    script.onerror = () => console.error('Failed to load Google Maps');

    document.head.appendChild(script);

    return () => {
      // Cleanup script if component unmounts
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, []);

  // Initialize map
  useEffect(() => {
    if (!isLoaded || !mapRef.current) return;

    const mapInstance = new google.maps.Map(mapRef.current, {
      center: mapConfig.center,
      zoom: mapConfig.zoom,
      styles: mapConfig.styles
    });

    setMap(mapInstance);
    
    if (onMapReady) {
      onMapReady(mapInstance);
    }
  }, [isLoaded, mapConfig]);

  // Add markers
  useEffect(() => {
    if (!map || !locations.length) return;

    // Clear existing markers
    markers.forEach(marker => marker.setMap(null));

    const newMarkers = locations.map((location) => {
      const marker = new google.maps.Marker({
        position: { lat: location.lat, lng: location.lng },
        map: map,
        title: location.name,
        icon: createMarkerIcon(location.type)
      });

      // Add info window
      const infoWindow = new google.maps.InfoWindow({
        content: createInfoWindowContent(location)
      });

      marker.addListener('click', () => {
        infoWindow.open(map, marker);
        if (onLocationClick) {
          onLocationClick(location);
        }
      });

      return marker;
    });

    setMarkers(newMarkers);
  }, [map, locations, onLocationClick]);

  const createInfoWindowContent = (location: Location): string => {
    return `
      <div style="padding: 8px; min-width: 200px;">
        <h3 style="font-weight: bold; font-size: 14px; margin-bottom: 4px; color: #1f2937;">
          ${location.name}
        </h3>
        <p style="font-size: 12px; color: #6b7280; margin-bottom: 2px;">
          📍 Location Type: ${location.type.replace('_', ' ')}
        </p>
        ${location.description ? `
          <p style="font-size: 12px; color: #4b5563; margin-top: 4px;">
            ${location.description}
          </p>
        ` : ''}
        <p style="font-size: 12px; color: #2563eb; text-transform: capitalize; font-weight: 500;">
          ${location.type.replace('_', ' ')}
        </p>
      </div>
    `;
  };

  if (!isLoaded) {
    return (
      <div className={`flex items-center justify-center bg-gray-100 ${className}`}>
        <div className="bg-white rounded-lg border p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Map</h3>
          <p className="text-gray-500">Loading Google Maps...</p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={mapRef}
      className={`w-full h-full ${className}`}
    />
  );
}
