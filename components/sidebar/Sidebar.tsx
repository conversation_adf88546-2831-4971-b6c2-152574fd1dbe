'use client';

import React, { useState } from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { SidebarProps, SearchFilters } from '@/types/map';
import SearchTab from './SearchTab';
import AlertsTab from './AlertsTab';
import RoutesTab from './RoutesTab';

export default function Sidebar({
  isOpen,
  onClose,
  locations,
  alerts,
  routes,
  statistics,
  onSearch,
  className = ''
}: SidebarProps) {
  const [activeTab, setActiveTab] = useState<'search' | 'alerts' | 'routes'>('search');
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    showPort: true,
    showCheckpost: true,
    tripCode: 'Trip Code',
    searchValue: ''
  });

  const handleFiltersChange = (filters: SearchFilters) => {
    setSearchFilters(filters);
    if (onSearch) {
      onSearch(filters);
    }
  };

  const handleReset = () => {
    const defaultFilters: SearchFilters = {
      showPort: true,
      showCheckpost: true,
      tripCode: 'Trip Code',
      searchValue: ''
    };
    setSearchFilters(defaultFilters);
    if (onSearch) {
      onSearch(defaultFilters);
    }
  };

  const tabs = [
    { id: 'search', label: 'Search', count: locations.length },
    { id: 'alerts', label: 'Alerts', count: alerts.length },
    { id: 'routes', label: 'Routes', count: routes.length }
  ] as const;

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={() => !isOpen && onClose()}
        className="fixed top-1/2 right-2 transform -translate-y-1/2 bg-blue-600 text-white rounded-full shadow-xl border-2 border-white p-4 hover:bg-blue-700 transition-all duration-200"
        style={{ zIndex: 1001 }}
        aria-label={isOpen ? 'Close sidebar' : 'Open sidebar'}
      >
        {isOpen ? (
          <ChevronRight className="w-6 h-6" />
        ) : (
          <ChevronLeft className="w-6 h-6" />
        )}
      </button>

      {/* Sidebar Content */}
      {isOpen && (
        <div
          className={`fixed right-0 bg-white shadow-xl border-l w-96 overflow-y-auto ${className}`}
          style={{
            top: '112px',
            bottom: '40px',
            zIndex: 999
          }}
        >
          <div className="h-full flex flex-col relative">
            {/* Tab Navigation */}
            <div className="flex border-b bg-gray-50">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 px-4 py-3 text-sm font-medium capitalize relative ${
                    activeTab === tab.id
                      ? 'bg-green-500 text-white'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                  }`}
                >
                  {tab.label}
                  {tab.count > 0 && (
                    <span className={`ml-2 px-2 py-1 rounded-full text-xs ${
                      activeTab === tab.id
                        ? 'bg-white text-green-500'
                        : 'bg-gray-200 text-gray-600'
                    }`}>
                      {tab.count}
                    </span>
                  )}
                </button>
              ))}
            </div>

            {/* Measure Distance Button */}
            <div className="p-4 border-b">
              <button className="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 transition-colors">
                Measure Distance
              </button>
            </div>

            {/* Tab Content */}
            <div className="flex-1 overflow-y-auto">
              {activeTab === 'search' && (
                <SearchTab
                  filters={searchFilters}
                  onFiltersChange={handleFiltersChange}
                  statistics={statistics}
                  onReset={handleReset}
                />
              )}

              {activeTab === 'alerts' && (
                <AlertsTab alerts={alerts} />
              )}

              {activeTab === 'routes' && (
                <RoutesTab
                  routes={routes}
                  userRoutes={routes.filter(route => route.id.startsWith('user-'))}
                  selectedRoutes={[]}
                  onRoutesChange={() => {}}
                  onRouteFilter={() => {}}
                />
              )}
            </div>

            {/* Close Button */}
            <div className="p-4 border-t bg-gray-50">
              <button
                onClick={onClose}
                className="w-full bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600 transition-colors"
              >
                Close Sidebar
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
